# PathForge AI - Production Docker Compose override
# Usage: docker compose -f compose.yaml -f compose.prod.yml up -d

services:
  pathforge_ai_service:
    image: registry.heroku.com/pathforge-ai/agent:${VERSION:-latest}
    restart: unless-stopped
    # Remove build context, use pre-built image
    build: null
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-}
      - LANGSMITH_API_KEY=${LANGSMITH_API_KEY:-}
      - LANGSMITH_PROJECT=${LANGSMITH_PROJECT:-}
    # Add resource limits
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    networks:
      - internal
      - web
    labels:
      # Traefik labels for reverse proxy (if using Traefik)
      - "traefik.enable=true"
      - "traefik.http.routers.pathforge-ai-service.rule=Host(`api.yourdomain.com`)"
      - "traefik.http.routers.pathforge-ai-service.tls=true"
      - "traefik.http.routers.pathforge-ai-service.tls.certresolver=letsencrypt"
      - "traefik.http.services.pathforge-ai-service.loadbalancer.server.port=8080"

  pathforge_ai_app:
    image: registry.heroku.com/pathforge-ai/streamlit:${VERSION:-latest}
    restart: unless-stopped
    # Remove build context, use pre-built image
    build: null
    environment:
      - AGENT_URL=http://pathforge_ai_service:8080
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    # Add resource limits
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    networks:
      - internal
      - web
    labels:
      # Traefik labels for reverse proxy (if using Traefik)
      - "traefik.enable=true"
      - "traefik.http.routers.pathforge-ai-app.rule=Host(`app.yourdomain.com`)"
      - "traefik.http.routers.pathforge-ai-app.tls=true"
      - "traefik.http.routers.pathforge-ai-app.tls.certresolver=letsencrypt"
      - "traefik.http.services.pathforge-ai-app.loadbalancer.server.port=8501"

networks:
  internal:
    driver: bridge
    internal: true
  web:
    external: true

# PathForge AI - DevOps & CI/CD Guide

This comprehensive guide covers all aspects of DevOps and CI/CD for PathForge AI, including setup, optimization, deployment, and best practices.

## Table of Contents

1. [CI/CD Pipeline Overview](#cicd-pipeline-overview)
2. [Conditional CI/CD Build System](#conditional-cicd-build-system)
3. [Docker Optimization](#docker-optimization)
4. [Docker Swarm Deployment](#docker-swarm-deployment)
5. [CI/CD Pipeline Refactoring](#cicd-pipeline-refactoring)
6. [Portainer Webhook Integration](#portainer-webhook-integration)
   - [Webhook Refactoring Overview](#webhook-refactoring-overview)
   - [Service Configuration](#service-configuration)
   - [How It Works](#how-it-works)
   - [Integration Points](#integration-points)
   - [Webhook Payload](#webhook-payload)
   - [Manual Webhook Script](#manual-webhook-script)
   - [Testing Results](#testing-results)
   - [Testing Webhooks](#testing-webhooks)
   - [Quick Commands Reference](#quick-commands-reference)
   - [Implementation Status](#implementation-status)
   - [Webhook Troubleshooting](#webhook-troubleshooting)
   - [Security Considerations](#security-considerations)
   - [Monitoring](#monitoring)
7. [Troubleshooting](#troubleshooting)
8. [Best Practices](#best-practices)
9. [AI-Assisted Testing Framework](#ai-assisted-testing-framework)
9. [Quick Commands Reference](#quick-commands-reference)
10. [Implementation Status](#implementation-status)

---

## CI/CD Pipeline Overview

PathForge AI uses GitHub Actions for CI/CD with the following workflows:

- **CI Pipeline**: Automated testing, code quality checks, and Docker builds
- **CD Pipeline**: Automated deployment to staging and production
- **PR Checks**: Additional validation for pull requests
- **Release Management**: Automated release creation and versioning
- **Maintenance**: Scheduled tasks for security and cleanup

### Prerequisites

#### Required Secrets

Configure the following secrets in your GitHub repository settings:

1. **CODECOV_TOKEN**: Token for code coverage reporting
2. **GITHUB_TOKEN**: Automatically provided by GitHub Actions

#### Optional Secrets (for deployment)

3. **STAGING_HOST**: SSH host for staging deployment
4. **STAGING_USER**: SSH user for staging deployment
5. **STAGING_KEY**: SSH private key for staging deployment
6. **PRODUCTION_HOST**: SSH host for production deployment
7. **PRODUCTION_USER**: SSH user for production deployment
8. **PRODUCTION_KEY**: SSH private key for production deployment

### Workflow Details

#### CI Pipeline (`.github/workflows/ci.yml`)

**Triggers**: Push to develop, Manual dispatch

**Jobs**:
1. **Test**: Runs unit tests, integration tests, and code quality checks
2. **Docker Build**: Builds Docker images for both services
3. **Integration Test**: Tests the full application stack

**Features**:
- Comprehensive code quality checks with auto-fixing
- Code coverage reporting with Codecov
- Parallel Docker builds for efficiency
- Unit testing without external dependencies

#### CD Pipeline (`.github/workflows/cd.yml`)

**Triggers**: Git tags, Manual dispatch

**Jobs**:
1. **Build and Push**: Builds and pushes Docker images to GitHub Container Registry
2. **Deploy Staging**: Deploys to staging environment (manual dispatch)
3. **Deploy Production**: Deploys to production environment (tags only)
4. **Security Scan**: Scans Docker images for vulnerabilities

**Features**:
- Multi-environment support
- Container registry integration
- Security scanning with Trivy
- Environment-specific configurations

#### PR Checks (`.github/workflows/pr.yml`)

**Triggers**: Pull request events

**Jobs**:
1. **PR Validation**: Validates PR title format
2. **Wait for Copilot**: Waits for GitHub Copilot response before allowing merge (10-minute timeout)
3. **Dependency Check**: Checks for security vulnerabilities
4. **Size Check**: Reports Docker image sizes
5. **Performance Test**: Basic performance validation

**GitHub Copilot Integration**:
- Automatically waits for GitHub Copilot to review PRs
- 10-minute timeout with fallback to manual review
- Adds warning comment if Copilot doesn't respond

#### Release Management (`.github/workflows/release.yml`)

**Triggers**: Git tags, Manual dispatch

**Features**:
- Automatic changelog generation
- Release notes with Docker image information
- Version bumping in pyproject.toml
- Pre-release detection

#### Maintenance (`.github/workflows/maintenance.yml`)

**Triggers**: Weekly schedule, Manual dispatch

**Jobs**:
1. **Cleanup Packages**: Removes old container images
2. **Security Audit**: Runs security scans
3. **Dependency Updates**: Checks for outdated dependencies
4. **Performance Baseline**: Establishes performance benchmarks

### Docker Images

Images are published to custom Docker registry:

- `registry.heroku.com/pathforge-ai-agent-service/web`
- `registry.heroku.com/pathforge-ai-streamlit-app/web`

**Tags**:
- `latest`: Latest tagged release
- `v0.x.0`: Semantic version tags (auto-incremented minor versions)

**Versioning**:
- Major version is fixed at 0
- Minor version auto-increments on each release
- Patch version is always 0
- Images are only built on tag creation events

---

## Conditional CI/CD Build System

The CI/CD system has been optimized to only build Docker images when the relevant code for each service has changed. This reduces build times, resource usage, and deployment overhead.

### Services and Their Dependencies

#### Agent Service
**Docker Image**: `agent_service`
**Triggers when these files change**:
- `src/agents/**` - Agent implementations
- `src/core/**` - Core modules (LLM, settings)
- `src/memory/**` - Memory implementations
- `src/schema/**` - Protocol schemas
- `src/service/**` - FastAPI service code
- `src/run_service.py` - Service entry point
- `docker/Dockerfile.service` - Service Dockerfile
- `docker/start_service.py` - Service startup script
- `docker/service_init_patch.py` - Service patches
- `docker/core_init_patch.py` - Core patches
- `.dockerignore.service` - Service dockerignore
- `requirements.txt`, `pyproject.toml`, `uv.lock` - Dependencies

#### Streamlit App
**Docker Image**: `streamlit_app`
**Triggers when these files change**:
- `src/client/**` - Agent client code
- `src/schema/**` - Protocol schemas (shared)
- `src/streamlit_app.py` - Streamlit app entry point
- `docker/Dockerfile.app` - App Dockerfile
- `.dockerignore.app` - App dockerignore
- `requirements.txt`, `pyproject.toml`, `uv.lock` - Dependencies

#### Frontend
**Docker Image**: `frontend`
**Triggers when these files change**:
- `src/frontend/**` - All frontend code
- `docker/Dockerfile.frontend` - Frontend Dockerfile
- `docker/nginx.conf` - Nginx configuration
- `.dockerignore.frontend` - Frontend dockerignore

### Conditional Build Workflow Structure

#### 1. Change Detection (`reusable-changes.yml`)
- Uses `dorny/paths-filter` to detect which files have changed
- Outputs boolean flags for each service: `agent_service`, `streamlit_app`, `frontend_docker`
- Also maintains legacy outputs for backward compatibility

#### 2. Conditional Docker Build (`reusable-conditional-docker-build.yml`)
- Automatically detects changes and builds only affected services
- Uses the enhanced `reusable-docker-build.yml` with conditional matrix generation
- Returns outputs indicating which services were built

#### 3. Enhanced Docker Build (`reusable-docker-build.yml`)
- Accepts parameters to control which services to build
- Generates dynamic build matrix based on input parameters
- Skips services that don't need to be built

### Usage in Workflows

#### CI Pipeline (`ci.yml`)
```yaml
docker-build:
  uses: ./.github/workflows/reusable-conditional-docker-build.yml
  permissions:
    contents: read
  with:
    push: false
```

#### CD Pipeline (`cd.yml`)
```yaml
build-and-push:
  uses: ./.github/workflows/reusable-conditional-docker-build.yml
  permissions:
    contents: read
    packages: write
  with:
    push: true
    registry: dockerhub.csharpp.com
    tag-prefix: ${{ needs.get-version.outputs.version }}
```

### Conditional Build Benefits

1. **Faster Builds**: Only builds what's needed
2. **Resource Efficiency**: Reduces CI/CD resource usage
3. **Faster Deployments**: Only deploys changed services
4. **Better Debugging**: Clear indication of what was built
5. **Cost Savings**: Reduced build minutes and registry storage

### Monitoring and Notifications

The system includes comprehensive Portainer webhook integration:

#### Automatic Webhooks (CI/CD)
- **Conditional Notifications**: Only sends webhooks for services that were actually built
- **Service-Specific URLs**: Each service has its own dedicated webhook endpoint
- **Dynamic Payload**: Includes image version, registry, and timestamp information
- **Error Handling**: Graceful failure handling with detailed logging

#### Manual Webhooks
Use the `notify-portainer.sh` script for manual webhook notifications:
```bash
# Notify specific service
./scripts/notify-portainer.sh frontend v1.2.3
./scripts/notify-portainer.sh agent latest
./scripts/notify-portainer.sh streamlit v1.2.3

# Notify all services
./scripts/notify-portainer.sh all v1.2.3
```

#### Portainer Service Configuration
| Service       | Portainer Name                         | Webhook ID                             |
| ------------- | -------------------------------------- | -------------------------------------- |
| Frontend      | `codepluse_pathforge_ai_frontend`      | `6a7068dc-feb7-40db-8dd1-c6d81604d03d` |
| Agent Service | `codepluse_pathforge_ai_agent_service` | `6d52c162-879b-4463-9e70-5ec5932bfbad` |
| Streamlit App | `codepluse_pathforge_ai_streamlit_app` | `0004e3e8-edb4-4c21-95bf-10f4d4785c68` |

### Backward Compatibility

The system maintains backward compatibility with existing workflows while adding the new conditional logic. Legacy change detection outputs are still available.

---

## Docker Optimization

The Streamlit app Docker image has been optimized using a multi-stage Debian slim build approach, balancing image size with compatibility.

### Optimization Strategies

#### 1. Multi-Stage Build

**Before**: Single-stage build with `python:3.12.3-slim`
**After**: Multi-stage build with `python:3.12.3-slim`

Benefits:
- Separates build dependencies from runtime dependencies
- Only includes necessary files in the final image
- Reduces attack surface by excluding build tools

#### 2. Debian Slim Base Image

**Current**: Debian slim (~120MB base)
**Previous**: Alpine Linux (~50MB base) - *Reverted due to glibc compatibility issues*

Benefits:
- Full glibc compatibility for compiled Python packages (e.g., tiktoken)
- Better compatibility with binary wheels
- Stable runtime environment for complex dependencies

#### 3. Minimal Dependencies

The Streamlit app only requires:
- `streamlit` - Web framework
- `httpx` - HTTP client for API communication
- `pydantic` - Data validation
- `python-dotenv` - Environment variable management

#### 4. Build Optimizations

- **Bytecode compilation**: `PYTHONOPTIMIZE=2` for smaller bytecode
- **No cache**: `--no-cache-dir` to avoid storing pip cache
- **Dependency grouping**: Uses `uv` with `--only-group client` for minimal installs
- **Layer optimization**: Combines commands to reduce layers

#### 5. Runtime Optimizations

- **Non-root user**: Security best practice
- **Streamlit-specific environment variables**: Disable unnecessary features
- **Health checks**: Built-in container health monitoring
- **Signal handling**: Proper process management with exec form

### Environment Variables

The following environment variables are set for optimization:

```dockerfile
# Python optimizations
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONPATH=/app
ENV PYTHONOPTIMIZE=2

# Streamlit optimizations
ENV STREAMLIT_SERVER_HEADLESS=true
ENV STREAMLIT_SERVER_ENABLE_CORS=false
ENV STREAMLIT_SERVER_ENABLE_XSRF_PROTECTION=false
ENV STREAMLIT_BROWSER_GATHER_USAGE_STATS=false
```

### Security Enhancements

1. **Non-root user**: Runs as `appuser` (UID 1001)
2. **Minimal attack surface**: Only essential packages installed
3. **No build tools in final image**: Build dependencies removed
4. **Health checks**: Enables container orchestration monitoring

### Size Comparison

The optimization reduces image size by approximately:
- **Base image**: ~70MB reduction (Debian slim → Alpine)
- **Dependencies**: ~50-100MB reduction (minimal vs full dependencies)
- **Build artifacts**: ~20-50MB reduction (multi-stage build)

**Estimated total reduction**: 140-220MB

### Usage

#### Building the Optimized Image

```bash
# Build the optimized Streamlit app image
docker build -f docker/Dockerfile.app -t streamlit_app:optimized .

# Check the image size
docker images streamlit_app:optimized
```

#### Running the Optimized Container

```bash
# Run with health checks
docker run -d \
  --name streamlit-app \
  -p 8501:8501 \
  -e AGENT_URL=http://agent-service:8080 \
  streamlit_app:optimized

# Check health status
docker ps --format "table {{.Names}}\t{{.Status}}"
```

---

## Docker Swarm Deployment

This section explains how to deploy the CodePluse Platform using Docker Swarm with Traefik integration.

### Prerequisites

1. **Docker Swarm**: Ensure Docker Swarm is initialized
   ```bash
   docker swarm init
   ```

2. **Traefik**: Have Traefik running with the `traefik_main` network
   ```bash
   docker network create --driver overlay traefik_main
   ```

3. **Domain Names**: Configure DNS to point to your Docker Swarm manager node

### Quick Start

1. **Copy environment file**:
   ```bash
   cp .env.swarm.example .env.production
   ```

2. **Edit configuration**:
   ```bash
   nano .env.production
   ```
   Update the following variables:
   - `AGENT_SERVICE_DOMAIN`: Your API domain (e.g., `api.yourdomain.com`)
   - `STREAMLIT_APP_DOMAIN`: Your app domain (e.g., `app.yourdomain.com`)
   - `POSTGRES_PASSWORD`: Secure database password
   - Add your API keys and other environment variables

3. **Deploy the stack**:
   ```bash
   ./scripts/deploy-swarm.sh v0.2.0 production
   ```

### Configuration

#### Environment Variables

| Variable               | Description                    | Default         |
| ---------------------- | ------------------------------ | --------------- |
| `VERSION`              | Docker image version to deploy | `latest`        |
| `OPENAI_API_KEY`       | OpenAI API key (required)      | -               |
| `AGENT_SERVICE_DOMAIN` | Domain for API service         | `api.localhost` |
| `STREAMLIT_APP_DOMAIN` | Domain for web app             | `app.localhost` |
| `CERT_RESOLVER`        | Traefik certificate resolver   | `letsencrypt`   |
| `ANTHROPIC_API_KEY`    | Anthropic API key (optional)   | -               |
| `LANGSMITH_API_KEY`    | LangSmith API key (optional)   | -               |

#### Service Configuration

The stack includes the following services:

##### Agent Service (API)
- **Replicas**: 2 (for high availability)
- **Resources**: 2GB memory limit, 1 CPU limit
- **Networks**: Internal + Traefik
- **Health checks**: HTTP endpoint `/info`
- **Rolling updates**: Zero-downtime deployments

##### Streamlit App
- **Replicas**: 2 (for high availability)
- **Resources**: 1GB memory limit, 0.5 CPU limit
- **Networks**: Internal + Traefik
- **Health checks**: HTTP endpoint `/healthz`
- **Rolling updates**: Zero-downtime deployments

### Traefik Integration

The stack is configured to work with an existing Traefik instance:

#### Labels Configuration

Each service includes Traefik labels for:
- **Routing**: Host-based routing to your domains
- **TLS**: Automatic SSL certificate management
- **Load Balancing**: Health checks and load distribution
- **Security**: Custom headers and middleware

#### Required Traefik Configuration

Ensure your Traefik instance has:
- Certificate resolver configured (Let's Encrypt recommended)
- Docker provider enabled
- `traefik_main` network attached

### Deployment Commands

#### Deploy New Version
```bash
./scripts/deploy-swarm.sh v0.3.0 production
```

#### Deploy to Staging
```bash
./scripts/deploy-swarm.sh latest staging
```

#### Check Stack Status
```bash
docker stack services codepluse-platform
docker stack ps codepluse-platform
```

#### View Service Logs
```bash
docker service logs codepluse-platform_agent_service
docker service logs codepluse-platform_streamlit_app
```

#### Update Single Service
```bash
docker service update --image dockerhub.csharpp.com/codepluse/agent_service:v0.3.0 codepluse-platform_agent_service
```

### Scaling

#### Scale Services
```bash
# Scale agent service to 3 replicas
docker service scale codepluse-platform_agent_service=3

# Scale streamlit app to 4 replicas
docker service scale codepluse-platform_streamlit_app=4
```

#### Resource Limits
Modify `docker-compose.swarm.yml` to adjust resource limits:
```yaml
deploy:
  resources:
    limits:
      memory: 4G
      cpus: '2.0'
    reservations:
      memory: 2G
      cpus: '1.0'
```

### Monitoring

#### Service Health
```bash
# Check service health
docker service ls
docker service ps codepluse-platform_agent_service --no-trunc
```

#### Resource Usage
```bash
# Monitor resource usage
docker stats $(docker ps --format "{{.Names}}" | grep codepluse-platform)
```

### Legacy Deployment Methods

#### Staging Deployment

Triggered by CD workflow on tag creation or manual dispatch:

```bash
# Manual staging deployment
./scripts/deploy.sh staging main
```

#### Production Deployment

Triggered by creating a git tag:

```bash
# Create and push a release tag
git tag v1.0.0
git push origin v1.0.0

# Manual production deployment
./scripts/deploy.sh production v1.0.0
```

#### Environment Configuration

Create environment-specific files:

- `.env.staging`: Staging environment variables
- `.env.production`: Production environment variables

Use `.env.example` as a template.

#### Portainer Integration

After successful Docker image builds, webhooks are automatically triggered to Portainer for deployment updates.

**Enhanced Webhook System**:
1. **Service-Specific Webhooks**: Each service has its own dedicated webhook endpoint
2. **Conditional Notifications**: Only services with new builds trigger webhooks
3. **Comprehensive Payload**: Includes image version, registry, timestamp, and service details
4. **Manual Control**: Use `scripts/notify-portainer.sh` for manual webhook notifications

**Webhook Endpoints**:
- Frontend: `https://portainer.csharpp.com/api/webhooks/6a7068dc-feb7-40db-8dd1-c6d81604d03d`
- Agent Service: `https://portainer.csharpp.com/api/webhooks/6d52c162-879b-4463-9e70-5ec5932bfbad`
- Streamlit App: `https://portainer.csharpp.com/api/webhooks/0004e3e8-edb4-4c21-95bf-10f4d4785c68`

**Manual Webhook Usage**:
```bash
# Notify specific service
./scripts/notify-portainer.sh frontend v1.2.3

# Notify all services
./scripts/notify-portainer.sh all v1.2.3
```

---

## CI/CD Pipeline Refactoring

This section summarizes the refactoring of GitHub Actions workflows to eliminate code duplication and improve maintainability by creating reusable components.

### Reusable Components Created

#### 1. Reusable Workflows

##### `.github/workflows/reusable-changes.yml`
- **Purpose**: Detect file changes to conditionally run jobs
- **Outputs**: `python` and `docker` flags
- **Used by**: CI, PR workflows

##### `.github/workflows/reusable-code-quality.yml`
- **Purpose**: Run code quality checks (Ruff, mypy)
- **Inputs**: Python version, UV version
- **Used by**: CI, PR workflows

##### `.github/workflows/reusable-test.yml`
- **Purpose**: Run unit tests with coverage
- **Inputs**: Python version, UV version, upload-coverage flag
- **Secrets**: All required API keys for tests
- **Used by**: CI, PR workflows

##### `.github/workflows/reusable-docker-build.yml`
- **Purpose**: Build and optionally push Docker images
- **Inputs**: push flag, registry, tag-prefix
- **Secrets**: Docker credentials
- **Used by**: CI, PR, CD workflows

##### `.github/workflows/reusable-docker-test.yml`
- **Purpose**: Test Docker images
- **Used by**: CI, PR workflows

#### 2. Composite Actions

##### `.github/actions/setup-python/action.yml`
- **Purpose**: Set up Python environment with UV and dependencies
- **Inputs**: Python version, UV version, install-dev flag
- **Used by**: All workflows requiring Python setup

##### `.github/actions/create-test-env/action.yml`
- **Purpose**: Create test environment file with secrets
- **Inputs**: All required API keys
- **Used by**: Test workflows (can be used in future)

### Workflows Refactored

#### 1. CI Workflow (`.github/workflows/ci.yml`)
**Before**: 181 lines with duplicated setup code
**After**: 51 lines using reusable components

**Changes**:
- Uses `reusable-changes.yml` for file change detection
- Uses `reusable-code-quality.yml` for code quality checks
- Uses `reusable-test.yml` for unit tests
- Uses `reusable-docker-build.yml` and `reusable-docker-test.yml` for Docker operations

#### 2. PR Workflow (`.github/workflows/pr.yml`)
**Before**: 392 lines with significant duplication
**After**: 264 lines using reusable components

**Changes**:
- Uses same reusable workflows as CI
- Maintains PR-specific jobs (pr-validation, dependency-check, size-check)
- Eliminates duplicate Python setup and test code

#### 3. CD Workflow (`.github/workflows/cd.yml`)
**Before**: 193 lines with custom Docker build logic
**After**: 143 lines using reusable Docker build

**Changes**:
- Added `get-version` job to extract version information
- Uses `reusable-docker-build.yml` for building and pushing images
- Updated dependent jobs to use version from `get-version` job

#### 4. Maintenance Workflow (`.github/workflows/maintenance.yml`)
**Before**: 158 lines with repeated Python setup
**After**: 140 lines using composite action

**Changes**:
- Uses `setup-python` composite action for all Python setup steps
- Eliminates duplicate Python/UV installation code

#### 5. Release Workflow (`.github/workflows/release.yml`)
**Before**: 118 lines with repeated Python setup
**After**: 106 lines using composite action

**Changes**:
- Uses `setup-python` composite action for Python setup steps

### Benefits Achieved

#### 1. Code Reduction
- **Total lines reduced**: ~300 lines across all workflows
- **Duplication eliminated**: Python setup, Docker build, test execution

#### 2. Maintainability
- **Single source of truth**: Changes to common operations only need to be made once
- **Consistency**: All workflows use the same setup procedures
- **Version management**: Python and UV versions centrally managed

#### 3. Reusability
- **Cross-workflow sharing**: Components can be used across different workflows
- **Future workflows**: New workflows can leverage existing components
- **External usage**: Reusable workflows can be called from other repositories

#### 4. Error Reduction
- **Standardized processes**: Reduces chance of configuration drift
- **Centralized secrets**: Test environment setup is consistent
- **Validated patterns**: Reusable components are tested across multiple workflows

### Usage Examples

#### Using Reusable Test Workflow
```yaml
test:
  uses: ./.github/workflows/reusable-test.yml
  with:
    python-version: "3.12"
    upload-coverage: true
  secrets:
    BRAVE_SEARCH_API_KEY: ${{ secrets.BRAVE_SEARCH_API_KEY }}
    # ... other secrets
```

#### Using Python Setup Action
```yaml
- name: Setup Python environment
  uses: ./.github/actions/setup-python
  with:
    python-version: "3.12"
    install-dev: true
```

### Future Improvements

1. **Additional Reusable Components**:
   - Security scanning workflow
   - Performance testing workflow
   - Deployment notification workflow

2. **Enhanced Parameterization**:
   - Configurable test patterns
   - Optional code quality checks
   - Flexible Docker build contexts

3. **Cross-Repository Usage**:
   - Make workflows callable from other repositories
   - Create organization-wide workflow templates

---

## Portainer Webhook Integration

The CodePluse Platform integrates with Portainer using webhooks to automatically update services when new Docker images are available. This enables continuous deployment by triggering service updates immediately after successful image builds.

### Webhook Refactoring Overview

The webhook system has been completely refactored to provide enhanced functionality, reliability, and monitoring capabilities:

#### Key Improvements
- ✅ **Service-Specific Webhooks**: Dedicated webhook endpoints for each service
- ✅ **Conditional Notifications**: Only services with actual builds trigger webhooks
- ✅ **Enhanced Logging**: Comprehensive logging with HTTP response codes
- ✅ **Manual Control**: Flexible manual webhook script for testing and deployment
- ✅ **Error Handling**: Robust error handling and graceful failures
- ✅ **Testing Infrastructure**: Comprehensive test suite for validation

### Service Configuration

#### Webhook URLs

The system is configured with three Portainer webhook endpoints:

| Service       | Portainer Service Name                 | Webhook URL                                                                       |
| ------------- | -------------------------------------- | --------------------------------------------------------------------------------- |
| Frontend      | `codepluse_pathforge_ai_frontend`      | `https://portainer.csharpp.com/api/webhooks/6a7068dc-feb7-40db-8dd1-c6d81604d03d` |
| Agent Service | `codepluse_pathforge_ai_agent_service` | `https://portainer.csharpp.com/api/webhooks/6d52c162-879b-4463-9e70-5ec5932bfbad` |
| Streamlit App | `codepluse_pathforge_ai_streamlit_app` | `https://portainer.csharpp.com/api/webhooks/0004e3e8-edb4-4c21-95bf-10f4d4785c68` |

#### Docker Images

| Service       | Docker Image                                       |
| ------------- | -------------------------------------------------- |
| Frontend      | `dockerhub.csharpp.com/pathforge-ai/frontend`      |
| Agent Service | `dockerhub.csharpp.com/pathforge-ai/agent_service` |
| Streamlit App | `dockerhub.csharpp.com/pathforge-ai/streamlit_app` |

#### Service Mapping Status

| Local Service | Docker Image                                       | Portainer Service                      | Webhook Status |
| ------------- | -------------------------------------------------- | -------------------------------------- | -------------- |
| Frontend      | `dockerhub.csharpp.com/pathforge-ai/frontend`      | `codepluse_pathforge_ai_frontend`      | ✅ Working      |
| Agent Service | `dockerhub.csharpp.com/pathforge-ai/agent_service` | `codepluse_pathforge_ai_agent_service` | ✅ Working      |
| Streamlit App | `dockerhub.csharpp.com/pathforge-ai/streamlit_app` | `codepluse_pathforge_ai_streamlit_app` | ✅ Working      |

### How It Works

#### Automatic Workflow (CI/CD)

1. **Code Push/Tag Creation** → Triggers CD workflow
2. **Conditional Build** → Only builds services with changes
3. **Image Push** → Pushes built images to registry
4. **Webhook Notifications** → Sends webhooks only for built services
5. **Portainer Update** → Services automatically update with new images

#### Manual Workflow

1. **Manual Deployment** → Run deployment scripts
2. **Webhook Notification** → Automatic webhook calls after deployment
3. **Portainer Update** → Services update with specified versions

Use the `notify-portainer.sh` script for manual webhook notifications:

```bash
# Notify specific service
./scripts/notify-portainer.sh frontend v1.2.3
./scripts/notify-portainer.sh agent latest
./scripts/notify-portainer.sh streamlit v1.2.3

# Notify all services
./scripts/notify-portainer.sh all v1.2.3
```

### Integration Points

#### CI/CD Pipeline

The webhook notifications are integrated into the CD workflow (`.github/workflows/cd.yml`):

- **Trigger**: After successful Docker image builds
- **Conditional**: Only sends webhooks for services that were actually built
- **Payload**: Includes image version, registry, and timestamp information
- **Error Handling**: Graceful failure handling with detailed logging

#### Deployment Scripts

Webhook notifications are integrated into deployment scripts:

- `scripts/deploy-swarm.sh` - Docker Swarm deployments with automatic webhook notifications
- `scripts/deploy-frontend.sh` - Frontend-specific deployments with webhook integration

#### Enhanced Features

1. **Refactored CD Workflow**: Service-specific webhooks with conditional notifications
2. **Manual Webhook Script**: Cross-platform compatible script with comprehensive help
3. **Enhanced Deployment Scripts**: Automatic webhook calls after successful deployments
4. **Test Infrastructure**: Comprehensive test suite for webhook system validation

### Webhook Payload

Each webhook sends the following JSON payload to Portainer:

```json
{
  "tag": "v1.2.3",
  "registry": "dockerhub.csharpp.com",
  "image": "dockerhub.csharpp.com/pathforge-ai/frontend:v1.2.3",
  "service": "codepluse_pathforge_ai_frontend",
  "timestamp": "2025-05-31T10:30:00Z",
  "source": "ci-cd-pipeline"
}
```

### Manual Webhook Script

#### Usage

```bash
./scripts/notify-portainer.sh [service_name] [version]
```

#### Arguments

- `service_name`: One of `frontend`, `agent`, `streamlit`, or `all`
- `version`: Docker image tag/version (default: `latest`)

#### Examples

```bash
# Notify frontend service with specific version
./scripts/notify-portainer.sh frontend v1.2.3

# Notify all services with latest tag
./scripts/notify-portainer.sh all latest

# Notify agent service with default version
./scripts/notify-portainer.sh agent

# Show help
./scripts/notify-portainer.sh --help
```

#### Output

The script provides detailed output including:
- Service being notified
- Full image name
- Webhook URL (truncated for security)
- HTTP response status
- Success/failure indication

### Testing Webhooks

#### Manual Testing

1. **Test single service**:
   ```bash
   ./scripts/notify-portainer.sh frontend latest
   ```

2. **Test all services**:
   ```bash
   ./scripts/notify-portainer.sh all latest
   ```

3. **Verify in Portainer**:
   - Check service update status in Portainer UI
   - Monitor service logs for update activity

#### CI/CD Testing

1. **Create a test tag**:
   ```bash
   git tag v1.0.0-test
   git push origin v1.0.0-test
   ```

2. **Monitor GitHub Actions**:
   - Check the CD workflow execution
   - Review webhook notification step output

3. **Verify in Portainer**:
   - Confirm services are updated with new image tags
   - Check deployment history

### Webhook Troubleshooting

#### Common Issues

1. **Webhook fails (HTTP 4xx/5xx)**:
   - Verify webhook URLs are correct
   - Check Portainer service configuration
   - Ensure network connectivity to Portainer

2. **Service not updating**:
   - Verify the webhook URL corresponds to the correct service
   - Check Portainer webhook configuration
   - Review Portainer logs

3. **Script not found**:
   - Ensure `notify-portainer.sh` is executable: `chmod +x scripts/notify-portainer.sh`
   - Verify script is in the correct location

#### Debug Commands

```bash
# Test webhook URL directly
curl -X POST "https://portainer.csharpp.com/api/webhooks/6a7068dc-feb7-40db-8dd1-c6d81604d03d" \
  -H "Content-Type: application/json" \
  -d '{"tag": "latest"}'

# Check script permissions
ls -la scripts/notify-portainer.sh

# View script help
./scripts/notify-portainer.sh --help
```

### Security Considerations

1. **Webhook URLs**: The webhook URLs contain sensitive tokens and should be protected
2. **Network Security**: Ensure secure communication between CI/CD and Portainer
3. **Access Control**: Limit access to webhook management in Portainer
4. **Monitoring**: Monitor webhook activity for unauthorized usage

### Monitoring

#### GitHub Actions

- Monitor CD workflow for webhook notification success/failure
- Review workflow logs for webhook response codes

#### Portainer

- Check service update history in Portainer UI
- Monitor service logs for deployment activity
- Review webhook activity in Portainer logs

#### Script Output

The `notify-portainer.sh` script provides detailed output:
- HTTP response codes
- Success/failure status
- Timing information
- Service details

### Testing Results

#### Webhook Functionality Tests

The webhook system has been thoroughly tested with all services responding correctly:

```bash
# ✅ All individual service webhooks working
$ ./scripts/notify-portainer.sh frontend test-version
[SUCCESS] Webhook sent successfully (HTTP 204)

$ ./scripts/notify-portainer.sh agent test-version  
[SUCCESS] Webhook sent successfully (HTTP 204)

$ ./scripts/notify-portainer.sh streamlit test-version
[SUCCESS] Webhook sent successfully (HTTP 204)

# ✅ All services webhook working
$ ./scripts/notify-portainer.sh all test-version-all
[SUCCESS] All webhook notifications completed successfully! (3/3)
```

#### HTTP Response Codes
- ✅ **HTTP 204**: Successful webhook delivery to all Portainer services
- ✅ **Error Handling**: Proper handling of network failures and invalid responses

#### Test Infrastructure

The project includes comprehensive webhook testing:

- **`scripts/test-webhooks.sh`**: Automated test suite for webhook system validation
- **Script validation**: Tests setup, permissions, and functionality
- **Error handling tests**: Validates robustness under failure conditions

### Key Benefits

The refactored webhook system provides significant improvements:

1. **Automated Deployments**: No manual intervention required for service updates
2. **Efficient Notifications**: Only updated services trigger webhooks
3. **Reliability**: Comprehensive error handling and logging
4. **Flexibility**: Support for both automatic and manual webhook triggering
5. **Monitoring**: Detailed logging for troubleshooting and monitoring
6. **Cross-Platform**: Compatible with bash/zsh shells and different environments

---

## Troubleshooting

### Common Issues

#### 1. CI/CD Pipeline Issues

**Test Failures**:
- Check PostgreSQL service health
- Verify environment variables are set correctly
- Review test logs for specific error messages

**Docker Build Failures**:
- Verify Dockerfile syntax and dependencies
- Check if base images are accessible
- Ensure build context includes all required files

**Deployment Failures**:
- Check environment variables and network connectivity
- Verify SSH keys and credentials
- Review deployment logs for specific errors

#### 2. Docker Swarm Issues

**Services not starting**:
```bash
docker service logs codepluse-platform_agent_service
```

**Traefik routing issues**:
- Check if `traefik_main` network exists
- Verify domain DNS configuration
- Check Traefik dashboard for service discovery

**API connection issues**:
- Verify API keys are properly set
- Check service logs for authentication errors
- Ensure external API endpoints are accessible

#### 3. Docker Image Issues

**Build failures**:
- Ensure all build dependencies are installed
- Check that all required files are copied
- Verify multi-stage build steps

**Runtime errors**:
- Check that all required files are copied
- Verify non-root user has proper permissions
- Review environment variable settings

**Permission issues**:
- Verify non-root user has proper permissions
- Check file ownership in Docker image

### Debugging Commands

#### CI/CD Debugging
```bash
# Check GitHub Actions logs for detailed error messages
# Run tests locally to reproduce issues
python -m pytest tests/ -v

# Verify environment configuration
docker-compose config
```

#### Docker Swarm Debugging
```bash
# Check service health
docker service ls
docker service ps codepluse-platform_agent_service --no-trunc

# Monitor resource usage
docker stats $(docker ps --format "{{.Names}}" | grep codepluse-platform)
```

#### Docker Image Debugging
```bash
# Inspect image layers
docker history streamlit_app:optimized

# Check running container
docker exec -it streamlit-app sh

# View logs
docker logs streamlit-app
```

### Rolling Back

#### Service Rollback
```bash
# Rollback to previous version
docker service rollback codepluse-platform_agent_service
docker service rollback codepluse-platform_streamlit_app
```

#### Complete Stack Removal
```bash
docker stack rm codepluse-platform
```

### Getting Help

1. Check existing GitHub Issues
2. Create a new issue with the bug report template
3. Include relevant logs and environment information

---

## Best Practices

### Development Workflow

1. Create feature branches from `develop`
2. Open PR against `develop` for review
3. Merge to `main` (CI can be run manually if needed)
4. Create tags for production releases

### Commit Messages

Use conventional commit format:
- `feat:` for new features
- `fix:` for bug fixes
- `docs:` for documentation
- `ci:` for CI/CD changes

### Testing

- Write tests for new features
- Maintain code coverage above 80%
- Test Docker builds locally before pushing

### Security

#### Container Security
1. **Environment Variables**: Store sensitive data in Docker secrets
2. **Network Isolation**: Services use internal network for inter-service communication
3. **TLS**: All external traffic is encrypted via Traefik
4. **Resource Limits**: Prevent resource exhaustion with proper limits
5. **Health Checks**: Automatic service recovery on failures

#### CI/CD Security
- Use GitHub Secrets for sensitive data
- Environment-specific secrets in deployment environments
- Regular rotation of API keys and passwords
- Container scanning for vulnerabilities
- Dependency security audits

### Monitoring

#### Code Coverage
- Codecov integration for coverage reporting
- Coverage badges in README
- PR comments with coverage changes

#### Performance
- Basic performance tests in CI
- Weekly performance baseline establishment
- Docker image size monitoring

#### Resource Usage
- Monitor service resource consumption
- Set appropriate resource limits and reservations
- Regular performance audits

### Docker Best Practices Applied

1. **Dependency Management**: Only install what's needed
2. **Layer Caching**: Optimize Docker layer caching
3. **Security**: Run as non-root user
4. **Monitoring**: Built-in health checks
5. **Performance**: Optimized Python runtime settings
6. **Maintenance**: Clear documentation and CI integration

### Future Improvements

#### Docker Optimization
1. **Distroless images**: Consider Google's distroless Python images
2. **Static linking**: Explore static compilation for even smaller images
3. **Dependency analysis**: Regular review of dependency tree
4. **Compression**: Implement image compression in registry

#### CI/CD Enhancements
1. **Security scanning workflow**: Enhanced vulnerability detection
2. **Performance testing workflow**: Automated performance benchmarks
3. **Deployment notification workflow**: Improved deployment tracking
4. **Cross-repository templates**: Organization-wide workflow standards
5. **AI-assisted testing integration**: Automated validation of AI-generated tests

#### Deployment Improvements
1. **Blue-green deployments**: Zero-downtime deployment strategy
2. **Canary releases**: Gradual rollout with monitoring
3. **A/B testing**: Feature flag integration
4. **Multi-region deployments**: Geographic distribution

#### Testing Infrastructure
1. **AI Test Validation**: Automated review system for AI-generated tests
2. **Test Quality Metrics**: Measure effectiveness of AI vs manual tests
3. **Cross-Component Testing**: Integration tests across frontend, agent, and streamlit
4. **Performance Benchmarking**: Automated performance regression testing

---

## AI-Assisted Testing Framework

The project implements a structured approach to AI-assisted test generation and validation:

### Test Organization

```
Component/
├── ai-tool-tests/           # AI-generated tests
│   ├── README.md           # Guidelines and documentation
│   ├── copilot/            # GitHub Copilot generated tests
│   ├── claude/             # Claude AI generated tests
│   ├── chatgpt/            # ChatGPT generated tests
│   └── other/              # Other AI tools
└── tests/                  # Manual and validated tests
```

### AI Testing Guidelines

#### Test Generation Process
1. **Prompt Engineering**: Use specific prompts for each AI tool
2. **Context Preservation**: Maintain conversation context for iterative improvements
3. **Documentation**: Record the AI tool and prompt used for each test
4. **Review Process**: Manual validation before integration

#### Quality Assurance
1. **Validation Checklist**: Verify test logic, assertions, and coverage
2. **Integration Testing**: Ensure AI tests work with existing test infrastructure
3. **Performance Impact**: Monitor test execution time and resource usage
4. **Maintenance**: Regular review and updates of AI-generated tests

#### Best Practices
1. **Naming Conventions**: Use prefixes indicating the AI tool (e.g., `test_copilot_`, `test_claude_`)
2. **Documentation**: Include comments explaining test purpose and modifications
3. **Incremental Integration**: Move validated tests to main test suite gradually
4. **Feedback Loop**: Use test results to improve AI prompts and generation quality

---

## Quick Commands Reference

#### Common Webhook Operations

```bash
# Test webhook system
./scripts/notify-portainer.sh all latest

# Deploy with webhooks  
./scripts/deploy-swarm.sh v1.2.3 production

# Notify specific service
./scripts/notify-portainer.sh frontend v1.2.3

# Check webhook help
./scripts/notify-portainer.sh --help

# Run webhook tests
./scripts/test-webhooks.sh
```

#### CI/CD Integration Commands

```bash
# Create a test tag to trigger CD workflow
git tag v1.0.0-test
git push origin v1.0.0-test

# Manual workflow dispatch
gh workflow run cd.yml
```

---

## Implementation Status

✅ **System Status**: All webhook functionality implemented and tested successfully!

🚀 **Ready for Production**: System is ready for automatic deployments via Portainer webhooks.

#### Completed Tasks

1. ✅ **Refactored CD Workflow**: Service-specific webhooks with conditional notifications
2. ✅ **Manual Webhook Script**: Cross-platform compatible with comprehensive features
3. ✅ **Enhanced Deployment Scripts**: Automatic webhook integration
4. ✅ **Documentation Consolidation**: Complete webhook guide integration
5. ✅ **AI Tool Testing Infrastructure**: Structured testing framework
6. ✅ **Test Infrastructure**: Comprehensive validation suite

#### Future Enhancements

1. **Webhook Authentication**: Add security tokens to webhook URLs
2. **Retry Logic**: Implement automatic retry for failed webhooks  
3. **Monitoring**: Add webhook success/failure metrics
4. **Environment-Specific**: Support different webhook URLs per environment
5. **Blue-Green Deployments**: Zero-downtime deployment integration

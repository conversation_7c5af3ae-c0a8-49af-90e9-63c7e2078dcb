# Multi-stage build for minimal Streamlit app image size
FROM python:3.12.3-slim AS builder

# Install build dependencies for Python packages
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    g++ \
    libc6-dev \
    libffi-dev \
    libssl-dev \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Upgrade pip to latest version to avoid hash issues
RUN pip install --no-cache-dir --upgrade pip

# Install uv for faster dependency resolution
RUN pip install --no-cache-dir uv

# Create a virtual environment and install client dependencies
RUN uv venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Install only the dependencies needed for the client application
# These are the minimal dependencies required for the Streamlit app
# Use --only-binary to prefer pre-built wheels and avoid compilation
RUN uv pip install --only-binary=:all: \
    httpx~=0.27.2 \
    pydantic~=2.11.5 \
    python-dotenv~=1.1.0 \
    streamlit~=1.45.1 \
    pypdf~=5.3.1 \
    pandas~=2.2.3 \
    langchain-core~=0.3.60 \
    langchain-openai~=0.3.18 \
    langchain-anthropic~=0.3.13 \
    langchain-aws~=0.2.23 \
    langchain-community~=0.3.24 \
    langchain-google-genai~=2.1.4 \
    langchain-google-vertexai~=2.0.7 \
    langchain-groq~=0.2.5 \
    langchain-ollama~=0.2.3 \
    langgraph~=0.3.34 \
    psycopg2-binary~=2.9.9 \
    openai~=1.81.0 \
    pydantic-settings~=2.6.1

# Clean up pip cache and temporary files
RUN pip cache purge && \
    rm -rf /tmp/* /var/tmp/* /root/.cache

# Runtime stage - minimal debian image
FROM python:3.12.3-slim AS runtime

# Install only essential runtime dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    libffi8 \
    libssl3 \
    ca-certificates \
    curl \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy virtual environment from builder
COPY --from=builder /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy only necessary source code
COPY src/client/ ./client/
COPY src/schema/ ./schema/
COPY src/agents/ ./agents/
COPY src/core/ ./core/
COPY src/streamlit_app.py .

# Set environment variables for optimization
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONPATH=/app
ENV PYTHONOPTIMIZE=2

# Streamlit specific optimizations
ENV STREAMLIT_SERVER_HEADLESS=true
ENV STREAMLIT_SERVER_ENABLE_CORS=false
ENV STREAMLIT_SERVER_ENABLE_XSRF_PROTECTION=false
ENV STREAMLIT_BROWSER_GATHER_USAGE_STATS=false

# Create non-root user for security
RUN groupadd -g 1001 appgroup && \
    useradd -u 1001 -g appgroup -m appuser

# Change ownership of app directory
RUN chown -R appuser:appgroup /app

USER appuser

EXPOSE 8501

# Health check for container orchestration
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8501/healthz || exit 1

# Use exec form for better signal handling
CMD ["streamlit", "run", "streamlit_app.py", "--server.port=8501", "--server.address=0.0.0.0"]

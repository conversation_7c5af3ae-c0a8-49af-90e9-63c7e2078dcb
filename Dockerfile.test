
FROM python:3.12.3-slim

WORKDIR /app

# Install minimal dependencies for testing
RUN pip install --no-cache-dir     streamlit==1.45.1     pypdf==5.3.1     langchain-core==0.3.60     langchain-openai==0.3.18     langgraph==0.3.34     psycopg2-binary==2.9.9     openai==1.81.0     pydantic==2.11.5     pydantic-settings==2.6.1     python-dotenv==1.1.0

# Copy source code
COPY src/client/ ./client/
COPY src/schema/ ./schema/
COPY src/agents/ ./agents/
COPY src/core/ ./core/
COPY src/streamlit_app.py .

# Set Python path
ENV PYTHONPATH=/app

# Test the imports
RUN python -c "
import sys
print('Testing critical imports...')
try:
    from agents.cv_extractor import process_cv_extraction
    print('SUCCESS: agents.cv_extractor import works!')
except ImportError as e:
    print(f'IMPORT ERROR: {e}')
    sys.exit(1)
except Exception as e:
    print(f'OTHER ERROR (expected): {e}')
    print('Import successful, error is configuration-related')

try:
    from pypdf import PdfReader
    print('SUCCESS: pypdf import works!')
except ImportError as e:
    print(f'IMPORT ERROR: {e}')
    sys.exit(1)

print('All critical imports successful!')
"

CMD ["echo", "Container build successful - imports work!"]

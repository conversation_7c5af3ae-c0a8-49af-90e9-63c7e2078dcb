version: '3.8'

services:
  pathforge_ai_agent_service:
    image: registry.heroku.com/pathforge-ai/agent:${VERSION:-latest}
    environment:
      # Add your environment variables here
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-}
      - LANGSMITH_API_KEY=${LANGSMITH_API_KEY:-}
      - LANGSMITH_PROJECT=${LANGSMITH_PROJECT:-}
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY:-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}
      - OPENROUTER_MODEL=${OPENROUTER_MODEL:-qwen/qwen3-235b-a22b}
      - OPENROUTER_BASEURL=${OPENROUTER_BASEURL:-https://openrouter.ai/api/v1}
      - OPENWEATHERMAP_API_KEY=${OPENWEATHERMAP_API_KEY:-********************************}
      - BRAVE_SEARCH_API_KEY=${BRAVE_SEARCH_API_KEY:-BSAm3V_RwMeOMpifscQLjSfMj5y034x}
      - DEFAULT_MODEL=${DEFAULT_MODEL:-openrouter}
      # Database Configuration
      - DATABASE_TYPE=postgres
      - POSTGRES_HOST=pathforge_ai_postgres
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-pathforge_secure_password}
      - POSTGRES_DB=${POSTGRES_DB:-pathforge_ai}
      - POSTGRES_PORT=5432
    networks:
      internal:
      traefik_main:
    depends_on:
      - pathforge_ai_postgres
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
        order: start-first
      rollback_config:
        parallelism: 1
        delay: 5s
        failure_action: pause
        order: stop-first
      # resources:
      #   limits:
      #     memory: 2G
      #     cpus: '1.0'
      #   reservations:
      #     memory: 1G
      #     cpus: '0.5'
      labels:
        # Traefik configuration
        - traefik.constraint-label=traefik_main
        - "traefik.enable=true"
        - "traefik.docker.network=traefik_main"

        # HTTP router
        - "traefik.http.routers.pathforge_ai-agent-service.rule=Host(`pathforge-ai-backend.csharpp.com`)"
        - "traefik.http.routers.pathforge_ai-agent-service.entrypoints=https"
        - "traefik.http.routers.pathforge_ai-agent-service.tls=true"
        - "traefik.http.routers.pathforge_ai-agent-service.tls.certresolver=le"

        # Service configuration
        - "traefik.http.services.pathforge_ai-agent-service.loadbalancer.server.port=8000"
        - traefik.http.routers.pathforge_ai-agent-service.service=pathforge_ai-agent-service


        
  pathforge_ai_streamlit_app:
    image: registry.heroku.com/pathforge-ai/streamlit:${VERSION:-latest}
    environment:
      # - AGENT_URL=https://pathforge-ai-backend.csharpp.com
      - AGENT_URL=http://pathforge_ai_agent_service:8000
      # - OPENAI_API_KEY=${OPENAI_API_KEY}
    networks:
      internal:
      traefik_main:
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
        order: start-first
      rollback_config:
        parallelism: 1
        delay: 5s
        failure_action: pause
        order: stop-first
      # resources:
      #   limits:
      #     memory: 1G
      #     cpus: '0.5'
      #   reservations:
      #     memory: 512M
      #     cpus: '0.25'
      labels:
        # Traefik configuration
        - traefik.constraint-label=traefik_main
        - "traefik.enable=true"
        - "traefik.docker.network=traefik_main"

        # HTTP router
        - "traefik.http.routers.pathforge_ai-streamlit-app.rule=Host(`pathforge_ai-streamlit.csharpp.com`)"
        - "traefik.http.routers.pathforge_ai-streamlit-app.entrypoints=https"
        - "traefik.http.routers.pathforge_ai-streamlit-app.tls=true"
        - "traefik.http.routers.pathforge_ai-streamlit-app.tls.certresolver=le"

        # Service configuration
        - "traefik.http.services.pathforge_ai-streamlit-app.loadbalancer.server.port=8501"
        - traefik.http.routers.pathforge_ai-streamlit-app.service=pathforge_ai-streamlit-app
        - traefik.http.routers.pathforge_ai-streamlit-app.middlewares=pathforge_ai-streamlit-app-basic-auth
        # Basic auth  
        # - traefik.http.middlewares.https-redirect.redirectscheme.scheme=https
        - traefik.http.middlewares.pathforge_ai-streamlit-app-basic-auth.basicauth.users=dcs:$$apr1$$rFShrRLe$$jxNtOI.1SLt7F8EQ7PPMs. # j2CcsOsuqrT7Lbgr
        - traefik.http.middlewares.pathforge_ai-streamlit-app-basic-auth.basicauth.removeheader=true
        # - traefik.http.middlewares.sslheader.headers.customrequestheaders.X-Forwarded-Proto=https

  pathforge_ai_  frontend:
    image: registry.heroku.com/pathforge-ai-frontend/web:${VERSION:-latest}
    environment:
      - REACT_APP_API_URL=https://pathforge-ai-api.csharpp.com
    networks:
      internal:
      traefik_main:
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
        order: start-first
      rollback_config:
        parallelism: 1
        delay: 5s
        failure_action: pause
        order: stop-first
      # resources:
      #   limits:
      #     memory: 512M
      #     cpus: '0.25'
      #   reservations:
      #     memory: 256M
      #     cpus: '0.1'
      labels:
        # Traefik configuration
        - traefik.constraint-label=traefik_main
        - "traefik.enable=true"
        - "traefik.docker.network=traefik_main"

        # HTTP router
        - "traefik.http.routers.pathforge_ai-frontend.rule=Host(`pathforge-ai.csharpp.com`)"
        - "traefik.http.routers.pathforge_ai-frontend.entrypoints=https"
        - "traefik.http.routers.pathforge_ai-frontend.tls=true"
        - "traefik.http.routers.pathforge_ai-frontend.tls.certresolver=le"

        # Service configuration
        - "traefik.http.services.pathforge_ai-frontend.loadbalancer.server.port=8080"
        - traefik.http.routers.pathforge_ai-frontend.service=pathforge_ai-frontend

  pathforge_ai_  backend:
    image: registry.heroku.com/pathforge-ai-backend/web:${VERSION:-latest}
    environment:
      # Database Configuration
      - DATABASE_URL=postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-pathforge_secure_password}@pathforge_ai_postgres:5432/${POSTGRES_BACKEND_DB:-pathforge_backend}
      - NODE_ENV=production
      # Application Configuration
      - PORT=8080
      - HOST=0.0.0.0
      # Security
      - JWT_SECRET=${JWT_SECRET:-pathforge_jwt_secret_change_in_production}
      - AUTH_SECRET=${AUTH_SECRET:-pathforge_auth_secret_change_in_production}
    networks:
      internal:
      traefik_main:
    depends_on:
      - pathforge_ai_postgres
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
        order: start-first
      rollback_config:
        parallelism: 1
        delay: 5s
        failure_action: pause
        order: stop-first
      # resources:
      #   limits:
      #     memory: 1G
      #     cpus: '0.5'
      #   reservations:
      #     memory: 512M
      #     cpus: '0.25'
      labels:
        # Traefik configuration
        - traefik.constraint-label=traefik_main
        - "traefik.enable=true"
        - "traefik.docker.network=traefik_main"

        # HTTP router
        - "traefik.http.routers.pathforge_ai-backend.rule=Host(`pathforge-ai-api.csharpp.com`)"
        - "traefik.http.routers.pathforge_ai-backend.entrypoints=https"
        - "traefik.http.routers.pathforge_ai-backend.tls=true"
        - "traefik.http.routers.pathforge_ai-backend.tls.certresolver=le"

        # Service configuration
        - "traefik.http.services.pathforge_ai-backend.loadbalancer.server.port=8080"
        - traefik.http.routers.pathforge_ai-backend.service=pathforge_ai-backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  pathforge_ai_postgres:
    image: postgres:16-alpine
    environment:
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-pathforge_secure_password}
      - POSTGRES_DB=${POSTGRES_DB:-pathforge_ai}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - pathforge_ai_postgres_data:/var/lib/postgresql/data
      # - ./scripts/init-multiple-databases.sh:/docker-entrypoint-initdb.d/init-multiple-databases.sh:ro
    networks:
      - internal
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
        order: start-first
      rollback_config:
        parallelism: 1
        delay: 5s
        failure_action: pause
        order: stop-first
      placement:
        constraints:
          - node.role == manager
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres} -d ${POSTGRES_DB:-pathforge_ai}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

volumes:
  pathforge_ai_postgres_data:
    driver: local

networks:
  internal:
    driver: overlay
    internal: true
  traefik_main:
    external: true

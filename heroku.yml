build:
  docker:
    web: docker/Dockerfile.frontend
    agent: docker/Dockerfile.service
    streamlit: docker/Dockerfile.app
    api: src/backend/Dockerfile

run:
  web: streamlit run src/streamlit_app.py --server.port=$PORT --server.address=0.0.0.0
  agent: python -m uvicorn src.service.api:app --host=0.0.0.0 --port=$PORT
  streamlit: streamlit run src/streamlit_app.py --server.port=$PORT --server.address=0.0.0.0
  api: python -m uvicorn src.backend.main:app --host=0.0.0.0 --port=$PORT
